import React, { useEffect, useState } from 'react';
import { YTHLocalization } from 'yth-ui';
import { Select, message, Input, Button, Form, Modal, Table, Tooltip } from 'antd';
import { CurrentUser } from '@/Constant';
import { IYTHColumnProps } from 'yth-ui/es/components/list';
import { queryMonitorIndexDetail } from '@/service/envApi';
import locales from '@/locales';
import style from './home.module.less';

type propsTypes = {
  open: boolean;
  onClose: () => void;
  isPark: boolean;
  selectCompany: onLineMonitorTypes.objType;
  companyList: onLineMonitorTypes.objType[];
};

/**
 * @description 监测指标 查看modal
 * @returns
 */
const MonotorIndexModal: React.FC<propsTypes> = (props) => {
  const [form] = Form.useForm();
  const { open, onClose, isPark, selectCompany, companyList } = props;
  const [tableList, setTableListt] = useState<onLineMonitorTypes.objType[]>();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);

  // 获取tableList数据
  const queryTableList = async (value?: { name?: string; companyId?: string }) => {
    setLoading(true);
    const data = await queryMonitorIndexDetail({
      name: value?.name ?? null,
      companyId: value?.companyId ?? null,
    });
    if (data.code === 200 && Array.isArray(data?.data)) {
      setTableListt(data?.data || []);
      setLoading(false);
    } else {
      message.error('请求数据出错，请刷新重试或联系管理员');
      setLoading(false);
    }
  };
  useEffect(() => {
    if (open) {
      if (isPark) {
        queryTableList({
          companyId: selectCompany?.value === 'all' ? undefined : selectCompany?.value,
        });
        form.setFieldsValue({
          companyId: selectCompany?.value === 'all' ? undefined : selectCompany?.value,
        });
      } else {
        queryTableList({ companyId: CurrentUser()?.unitCode });
        form.setFieldsValue({ companyId: CurrentUser()?.unitCode });
      }
    }
    setIsModalOpen(open);
    return () => {
      form.resetFields();
    };
  }, [open, isPark, CurrentUser, selectCompany]);

  const columnsData: IYTHColumnProps[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      fixed: 'left',
      render: (v, r, index) => {
        return index + 1;
      },
    },
    {
      title: '单位名称',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 200,
      align: 'center',
      render: (v) => {
        return (
          <Tooltip title={v ?? '-'}>
            <div className={style['table-columns-title']}>{v ?? '-'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '设备名称',
      dataIndex: 'devideName',
      key: 'devideName',
      width: 180,
      align: 'center',
      render: (v) => {
        return (
          <Tooltip title={v ?? '-'}>
            <div className={style['table-columns-title']}>{v ?? '-'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '监测指标名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      align: 'center',
      render: (v) => {
        return (
          <Tooltip title={v ?? '-'}>
            <div className={style['table-columns-title']}>{v ?? '-'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '监测指标编码',
      dataIndex: 'code',
      key: 'code',
      width: 180,
      align: 'center',
      render: (v) => {
        return (
          <Tooltip title={v ?? '-'}>
            <div className={style['table-columns-title']}>{v ?? '-'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '计量单位',
      dataIndex: 'measureUnit',
      key: 'measureUnit',
      width: 100,
      align: 'center',
    },
    {
      title: '一级阈值上限',
      dataIndex: 'firstLevelMax',
      key: 'firstLevelMax',
      width: 100,
      align: 'center',
    },
    {
      title: '一级阈值下限',
      dataIndex: 'firstLevelMin',
      key: 'firstLevelMin',
      width: 100,
      align: 'center',
    },
    {
      title: '二级阈值上限',
      dataIndex: 'secondLevelMax',
      key: 'secondLevelMax',
      width: 100,
      align: 'center',
    },
    {
      title: '二级阈值下限',
      dataIndex: 'secondLevelMin',
      key: 'secondLevelMin',
      width: 100,
      align: 'center',
    },
  ];

  const onFinish = (values) => {
    queryTableList(values);
  };

  return (
    <Modal
      title="监测指标详情"
      visible={isModalOpen}
      onCancel={() => {
        setIsModalOpen(false);
        if (onClose) onClose();
      }}
      destroyOnClose
      width="70%"
      footer={null}
    >
      <Form
        name="basic"
        layout="inline"
        form={form}
        style={{ marginBottom: 15, fontSize: 14 }}
        onFinish={onFinish}
        onReset={() => {
          if (isPark) {
            queryTableList();
          } else {
            queryTableList({ companyId: CurrentUser()?.unitCode });
            form.setFieldsValue({ companyId: CurrentUser()?.unitCode });
          }
        }}
        autoComplete="off"
      >
        <Form.Item label="单位名称" name="companyId">
          <Select
            showSearch
            placeholder="请选择"
            optionFilterProp="label"
            style={{ width: 222 }}
            disabled={!isPark}
            options={companyList?.map((item) => {
              return {
                value: item.companyId,
                label: item.supplyUnit,
              };
            })}
          />
        </Form.Item>
        <Form.Item label="设备名称" name="name">
          <Input placeholder="请输入设备名称" />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            查询
          </Button>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="reset">
            重置
          </Button>
        </Form.Item>
      </Form>
      <Table
        loading={loading}
        dataSource={tableList}
        rowKey="id"
        className={style['device-table']}
        bordered
        rowClassName={(_, index) => {
          if (index % 2 === 0) {
            return style['even-row'];
          }
          return style['odd-row'];
        }}
        pagination={false}
        columns={columnsData}
        scroll={{ y: 500 }}
      />
    </Modal>
  );
};

export default YTHLocalization.withLocal(MonotorIndexModal, locales, YTHLocalization.getLanguage());
