import React, { useState, useEffect } from 'react';
import { Button, Modal, Transfer } from 'antd';

interface TraceObject {
  key: string;
  title: string;
  description: string;
}

interface TraceObjectSelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  disabled?: boolean;
  isTraceEnabled?: boolean;
  type?: 'view' | 'edit' | 'add';
}

const TraceObjectSelector: React.FC<TraceObjectSelectorProps> = ({
  value = [],
  onChange,
  disabled = false,
  isTraceEnabled = false,
  type = 'edit',
}) => {
  const [transferVisible, setTransferVisible] = useState<boolean>(false);
  const [transferTargetKeys, setTransferTargetKeys] = useState<string[]>([]);

  // 模拟溯源对象数据
  const mockTraceData: TraceObject[] = [
    { key: '1', title: '污染源企业A', description: '化工企业' },
    { key: '2', title: '污染源企业B', description: '钢铁企业' },
    { key: '3', title: '污染源企业C', description: '电力企业' },
    { key: '4', title: '污染源企业D', description: '水泥企业' },
    { key: '5', title: '污染源企业E', description: '造纸企业' },
    { key: '6', title: '污染源企业F', description: '石化企业' },
    { key: '7', title: '污染源企业G', description: '纺织企业' },
    { key: '8', title: '污染源企业H', description: '食品企业' },
  ];

  useEffect(() => {
    setTransferTargetKeys(value || []);
  }, [value]);

  // 打开穿梭框
  const openModal = () => {
    if (disabled || type === 'view' || !isTraceEnabled) return;
    setTransferVisible(true);
  };

  // 穿梭框确认
  const handleTransferOk = () => {
    if (onChange) {
      onChange(transferTargetKeys);
    }
    setTransferVisible(false);
  };

  // 穿梭框取消
  const handleTransferCancel = () => {
    setTransferTargetKeys(value || []);
    setTransferVisible(false);
  };

  // 穿梭框数据变化
  const handleTransferChange = (targetKeys: string[]) => {
    setTransferTargetKeys(targetKeys);
  };

  // 如果未开启溯源，显示 "-"
  if (!isTraceEnabled) {
    return <span>-</span>;
  }

  const count = value?.length || 0;
  const displayText = count > 0 ? `已选择 ${count} 个对象` : '点击选择溯源对象';

  return (
    <>
      <Button
        type="link"
        size="small"
        disabled={disabled || type === 'view'}
        onClick={openModal}
        style={{
          padding: 0,
          height: 'auto',
          color: type === 'view' || disabled ? '#666' : '#1890ff',
        }}
      >
        {displayText}
      </Button>

      <Modal
        title="选择溯源对象"
        open={transferVisible}
        onOk={handleTransferOk}
        onCancel={handleTransferCancel}
        width={800}
        okText="确定"
        cancelText="取消"
      >
        <Transfer
          dataSource={mockTraceData}
          targetKeys={transferTargetKeys}
          onChange={handleTransferChange}
          render={(item) => item.title}
          titles={['可选对象', '已选对象']}
          showSearch
          listStyle={{
            width: 300,
            height: 400,
          }}
        />
      </Modal>
    </>
  );
};

export default TraceObjectSelector;
