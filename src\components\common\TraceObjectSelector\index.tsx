import React, { useState, useCallback, useEffect } from 'react';
import { Input, Modal, Transfer, ConfigProvider, Spin, Button } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { envRequest } from '@/request';

/**
 * 溯源对象数据结构
 */
interface TraceObject {
  key: string; // 唯一标识
  title: string; // 显示名称
  description: string; // 描述信息
  company: string; // 公司名称
  traceIndex: string; // 溯源指标
}

/**
 * 溯源对象选择器组件属性
 */
interface TraceObjectSelectorProps {
  value?: string[]; // 已选择的溯源对象ID数组
  onChange?: (value: string[]) => void; // 选择变化时的回调函数
  disabled?: boolean; // 是否禁用
  isTraceEnabled?: boolean; // 是否开启溯源功能
  type?: 'view' | 'edit' | 'add'; // 组件状态类型
}

/**
 * 溯源对象选择器组件
 * 用于环境质量监测中的溯源对象选择功能
 * 显示为输入框样式，点击打开穿梭框进行选择
 */
const TraceObjectSelector: React.FC<TraceObjectSelectorProps> = ({
  value = [], // 已选择的溯源对象ID数组，默认为空数组
  onChange, // 选择变化时的回调函数
  disabled = false, // 是否禁用，默认为false
  isTraceEnabled = false, // 是否开启溯源功能，默认为false
  type = 'edit', // 组件状态类型，默认为编辑模式
}) => {
  // 穿梭框显示状态
  const [transferVisible, setTransferVisible] = useState<boolean>(false);
  // 穿梭框中已选择的keys，初始化为传入的value
  const [transferTargetKeys, setTransferTargetKeys] = useState<string[]>(value || []);
  // 溯源对象数据
  const [traceData, setTraceData] = useState<TraceObject[]>([]);
  // 数据加载状态
  const [loading, setLoading] = useState<boolean>(false);
  // 分页状态
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [total, setTotal] = useState<number>(0);
  // 搜索状态
  const [isSearchMode, setIsSearchMode] = useState<boolean>(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');

  /**
   * 获取溯源对象数据
   * 分页累加加载，适合500-1000条数据
   */
  const fetchTraceData = useCallback(async (page = 1, isLoadMore = false) => {
    try {
      setLoading(true);
      // 调用接口获取污染源企业数据
      const response = await envRequest('/pollution-source/list', {
        method: 'POST',
        data: {
          currentPage: page,
          pageSize: 100, // 每次加载100条，分批加载
          condition: {
            // 可以添加查询条件，比如企业类型等
          },
        },
      });

      if (response && response.list) {
        // 将接口返回的数据转换为穿梭框需要的格式
        const formattedData: TraceObject[] = response.list.map((item: any, index: number) => {
          const company = item.name || item.companyName || item.unitName || `企业${index + 1}`;
          const traceIndex = item.traceIndex || item.indexName || item.indicator || '未知指标';

          return {
            key: item.id || `trace_${page}_${index}`,
            title: `${company} - ${traceIndex}`, // 用于搜索和显示
            description: item.industryType || item.type || item.description || '企业',
            company,
            traceIndex,
          };
        });

        // 累加数据或重置数据
        if (isLoadMore) {
          setTraceData((prev) => [...prev, ...formattedData]);
        } else {
          setTraceData(formattedData);
        }

        // 更新分页状态
        setCurrentPage(page);
        setTotal(response.total || response.totalCount || 0);
        setHasMore(formattedData.length === 100); // 如果返回数据少于100条，说明没有更多数据
      } else {
        // 如果接口没有数据或失败，使用备用模拟数据
        const fallbackData: TraceObject[] = [
          {
            key: 'comp_001_hg',
            title: '云南安宁化工有限公司 - SO2排放量',
            description: '化工企业',
            company: '云南安宁化工有限公司',
            traceIndex: 'SO2排放量',
          },
          {
            key: 'comp_002_gt',
            title: '安宁钢铁集团有限公司 - NOx排放量',
            description: '钢铁企业',
            company: '安宁钢铁集团有限公司',
            traceIndex: 'NOx排放量',
          },
          {
            key: 'comp_003_dl',
            title: '安宁电力发展有限公司 - 烟尘排放量',
            description: '电力企业',
            company: '安宁电力发展有限公司',
            traceIndex: '烟尘排放量',
          },
          {
            key: 'comp_004_sn',
            title: '云南安宁水泥股份有限公司 - 颗粒物排放量',
            description: '水泥企业',
            company: '云南安宁水泥股份有限公司',
            traceIndex: '颗粒物排放量',
          },
          {
            key: 'comp_005_zz',
            title: '安宁造纸工业有限公司 - COD排放量',
            description: '造纸企业',
            company: '安宁造纸工业有限公司',
            traceIndex: 'COD排放量',
          },
          {
            key: 'comp_006_sh',
            title: '中石化安宁分公司 - VOCs排放量',
            description: '石化企业',
            company: '中石化安宁分公司',
            traceIndex: 'VOCs排放量',
          },
          {
            key: 'comp_007_fz',
            title: '安宁纺织印染有限公司 - 氨氮排放量',
            description: '纺织企业',
            company: '安宁纺织印染有限公司',
            traceIndex: '氨氮排放量',
          },
          {
            key: 'comp_008_sp',
            title: '云南安宁食品加工有限公司 - 总磷排放量',
            description: '食品企业',
            company: '云南安宁食品加工有限公司',
            traceIndex: '总磷排放量',
          },
          {
            key: 'comp_009_yy',
            title: '安宁制药股份有限公司 - 重金属排放量',
            description: '医药企业',
            company: '安宁制药股份有限公司',
            traceIndex: '重金属排放量',
          },
          {
            key: 'comp_010_jc',
            title: '安宁建材集团有限公司 - 粉尘排放量',
            description: '建材企业',
            company: '安宁建材集团有限公司',
            traceIndex: '粉尘排放量',
          },
          {
            key: 'comp_011_jx',
            title: '云南安宁机械制造有限公司 - 噪声排放量',
            description: '机械企业',
            company: '云南安宁机械制造有限公司',
            traceIndex: '噪声排放量',
          },
          {
            key: 'comp_012_dz',
            title: '安宁电子科技有限公司 - 废水排放量',
            description: '电子企业',
            company: '安宁电子科技有限公司',
            traceIndex: '废水排放量',
          },
          {
            key: 'comp_013_qg',
            title: '安宁轻工业发展有限公司 - 固废产生量',
            description: '轻工企业',
            company: '安宁轻工业发展有限公司',
            traceIndex: '固废产生量',
          },
          {
            key: 'comp_014_ys',
            title: '云南安宁有色金属有限公司 - 重金属排放量',
            description: '有色金属企业',
            company: '云南安宁有色金属有限公司',
            traceIndex: '重金属排放量',
          },
          {
            key: 'comp_015_mt',
            title: '安宁煤炭工业有限公司 - 煤尘排放量',
            description: '煤炭企业',
            company: '安宁煤炭工业有限公司',
            traceIndex: '煤尘排放量',
          },
        ];
        setTraceData(fallbackData);
      }
    } catch {
      // 接口调用失败时使用备用数据，静默处理错误
      const fallbackData: TraceObject[] = [
        {
          key: 'comp_001_hg',
          title: '云南安宁化工有限公司 - SO2排放量',
          description: '化工企业',
          company: '云南安宁化工有限公司',
          traceIndex: 'SO2排放量',
        },
        {
          key: 'comp_002_gt',
          title: '安宁钢铁集团有限公司 - NOx排放量',
          description: '钢铁企业',
          company: '安宁钢铁集团有限公司',
          traceIndex: 'NOx排放量',
        },
        {
          key: 'comp_003_dl',
          title: '安宁电力发展有限公司 - 烟尘排放量',
          description: '电力企业',
          company: '安宁电力发展有限公司',
          traceIndex: '烟尘排放量',
        },
        {
          key: 'comp_004_sn',
          title: '云南安宁水泥股份有限公司 - 颗粒物排放量',
          description: '水泥企业',
          company: '云南安宁水泥股份有限公司',
          traceIndex: '颗粒物排放量',
        },
        {
          key: 'comp_005_zz',
          title: '安宁造纸工业有限公司 - COD排放量',
          description: '造纸企业',
          company: '安宁造纸工业有限公司',
          traceIndex: 'COD排放量',
        },
        {
          key: 'comp_006_sh',
          title: '中石化安宁分公司 - VOCs排放量',
          description: '石化企业',
          company: '中石化安宁分公司',
          traceIndex: 'VOCs排放量',
        },
        {
          key: 'comp_007_fz',
          title: '安宁纺织印染有限公司 - 氨氮排放量',
          description: '纺织企业',
          company: '安宁纺织印染有限公司',
          traceIndex: '氨氮排放量',
        },
        {
          key: 'comp_008_sp',
          title: '云南安宁食品加工有限公司 - 总磷排放量',
          description: '食品企业',
          company: '云南安宁食品加工有限公司',
          traceIndex: '总磷排放量',
        },
        {
          key: 'comp_009_yy',
          title: '安宁制药股份有限公司 - 重金属排放量',
          description: '医药企业',
          company: '安宁制药股份有限公司',
          traceIndex: '重金属排放量',
        },
        {
          key: 'comp_010_jc',
          title: '安宁建材集团有限公司 - 粉尘排放量',
          description: '建材企业',
          company: '安宁建材集团有限公司',
          traceIndex: '粉尘排放量',
        },
        {
          key: 'comp_011_jx',
          title: '云南安宁机械制造有限公司 - 噪声排放量',
          description: '机械企业',
          company: '云南安宁机械制造有限公司',
          traceIndex: '噪声排放量',
        },
        {
          key: 'comp_012_dz',
          title: '安宁电子科技有限公司 - 废水排放量',
          description: '电子企业',
          company: '安宁电子科技有限公司',
          traceIndex: '废水排放量',
        },
        {
          key: 'comp_013_qg',
          title: '安宁轻工业发展有限公司 - 固废产生量',
          description: '轻工企业',
          company: '安宁轻工业发展有限公司',
          traceIndex: '固废产生量',
        },
        {
          key: 'comp_014_ys',
          title: '云南安宁有色金属有限公司 - 重金属排放量',
          description: '有色金属企业',
          company: '云南安宁有色金属有限公司',
          traceIndex: '重金属排放量',
        },
        {
          key: 'comp_015_mt',
          title: '安宁煤炭工业有限公司 - 煤尘排放量',
          description: '煤炭企业',
          company: '安宁煤炭工业有限公司',
          traceIndex: '煤尘排放量',
        },
      ];
      setTraceData(fallbackData);
    } finally {
      setLoading(false);
    }
  }, []);

  // 组件挂载时获取第一页数据
  useEffect(() => {
    fetchTraceData(1, false); // 加载第一页数据
  }, [fetchTraceData]);

  /**
   * 打开穿梭框
   * 在禁用、查看模式或未开启溯源时不允许打开
   */
  const openModal = useCallback(() => {
    if (disabled || type === 'view' || !isTraceEnabled) return;
    // 确保打开时使用最新的值，避免状态不同步
    setTransferTargetKeys(value || []);
    setTransferVisible(true);
  }, [disabled, type, isTraceEnabled, value]);

  /**
   * 服务端搜索功能
   * 根据关键词搜索所有匹配的数据
   */
  const serverSearch = useCallback(async (keyword: string) => {
    try {
      setLoading(true);
      const response = await envRequest('/pollution-source/search', {
        method: 'POST',
        data: {
          keyword: keyword.trim(),
          pageSize: 1000, // 搜索时获取所有匹配结果
          condition: {
            // 可以添加其他搜索条件
          },
        },
      });

      if (response && response.list) {
        const formattedData: TraceObject[] = response.list.map((item: any, index: number) => {
          const company = item.name || item.companyName || item.unitName || `企业${index + 1}`;
          const traceIndex = item.traceIndex || item.indexName || item.indicator || '未知指标';

          return {
            key: item.id || `search_${index}`,
            title: `${company} - ${traceIndex}`,
            description: item.industryType || item.type || item.description || '企业',
            company,
            traceIndex,
          };
        });

        // 搜索模式：替换数据，不累加
        setTraceData(formattedData);
        setTotal(response.total || response.totalCount || formattedData.length);
        setHasMore(false); // 搜索模式下不显示加载更多
        setIsSearchMode(true);
        setSearchKeyword(keyword);
      } else {
        // 搜索无结果
        setTraceData([]);
        setTotal(0);
        setHasMore(false);
        setIsSearchMode(true);
        setSearchKeyword(keyword);
      }
    } catch {
      // 搜索失败，保持当前数据
      console.warn('搜索失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 清空搜索，恢复分页模式
   */
  const clearSearch = useCallback(() => {
    setIsSearchMode(false);
    setSearchKeyword('');
    setCurrentPage(1);
    setHasMore(true);
    // 重新加载第一页数据
    fetchTraceData(1, false);
  }, [fetchTraceData]);

  /**
   * 加载更多数据
   */
  const loadMoreData = useCallback(() => {
    if (!loading && hasMore) {
      fetchTraceData(currentPage + 1, true);
    }
  }, [loading, hasMore, currentPage, fetchTraceData]);

  /**
   * 渲染加载更多按钮
   */
  const renderLoadMoreButton = useCallback(
    (direction: string) => {
      // 只在左侧（可选对象）显示加载更多按钮
      if (direction === 'left') {
        // 搜索模式下显示搜索状态，分页模式下显示加载更多
        if (isSearchMode) {
          return (
            <div style={{ textAlign: 'center', padding: '8px' }}>
              <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                搜索"{searchKeyword}": 找到 {traceData.length} 条结果
              </div>
              <Button size="small" onClick={clearSearch} style={{ width: '100%' }}>
                清空搜索，返回浏览模式
              </Button>
            </div>
          );
        } else {
          return (
            <div style={{ textAlign: 'center', padding: '8px' }}>
              <Button
                size="small"
                onClick={loadMoreData}
                loading={loading}
                disabled={!hasMore}
                style={{ width: '100%' }}
              >
                {hasMore ? `加载更多 (已加载${traceData.length}/${total}条)` : '已加载全部数据'}
              </Button>
            </div>
          );
        }
      }
      return null;
    },
    [
      loadMoreData,
      loading,
      hasMore,
      traceData.length,
      total,
      isSearchMode,
      searchKeyword,
      clearSearch,
    ],
  );

  /**
   * 处理搜索输入
   * 区分服务端搜索和本地搜索
   */
  const handleSearch = useCallback(
    (direction: 'left' | 'right', value: string) => {
      if (direction === 'left') {
        const keyword = value.trim();
        if (keyword) {
          // 有搜索关键词：使用服务端搜索
          serverSearch(keyword);
        } else {
          // 清空搜索：恢复分页模式
          clearSearch();
        }
      }
      // 右侧搜索使用默认的本地搜索
      return true;
    },
    [serverSearch, clearSearch],
  );

  /**
   * 穿梭框确认操作
   * 将选中的数据通过onChange回调传递给父组件
   */
  const handleTransferOk = useCallback(() => {
    if (onChange) {
      onChange(transferTargetKeys);
    }
    setTransferVisible(false);
  }, [onChange, transferTargetKeys]);

  /**
   * 穿梭框取消操作
   * 恢复到原始值，不保存任何更改
   */
  const handleTransferCancel = useCallback(() => {
    setTransferTargetKeys(value || []);
    setTransferVisible(false);
  }, [value]);

  /**
   * 穿梭框数据变化处理
   * 实时更新选中的keys
   */
  const handleTransferChange = useCallback((targetKeys: string[]) => {
    setTransferTargetKeys(targetKeys);
  }, []);

  /**
   * 渲染穿梭框选项
   * 使用 title 字段确保搜索功能正常
   */
  const renderItem = useCallback((item: TraceObject) => {
    // 直接返回 title，它已经包含了 "公司 - 指标" 的格式
    return item.title;
  }, []);

  // 如果未开启溯源，显示 "-"
  if (!isTraceEnabled) {
    return (
      <Input
        value="-"
        readOnly
        disabled
        style={{
          cursor: 'default',
          backgroundColor: '#f5f5f5',
        }}
      />
    );
  }

  // 计算已选择的对象数量
  const count = value?.length || 0;
  // 显示文本：有选择时显示数量，无选择时显示"请选择"
  const displayText = count > 0 ? count.toString() : '请选择';

  return (
    <>
      {/* 输入框样式的选择器 */}
      <Input
        value={displayText}
        readOnly // 只读，不允许直接输入
        placeholder="请选择"
        style={{
          cursor: disabled || type === 'view' ? 'default' : 'pointer',
          backgroundColor: disabled || type === 'view' ? '#f5f5f5' : '#fff',
        }}
        onClick={openModal}
        disabled={disabled || type === 'view'}
      />

      {/* 溯源对象选择穿梭框 */}
      <ConfigProvider locale={zhCN}>
        <Modal
          title="选择溯源对象"
          open={transferVisible}
          onOk={handleTransferOk}
          onCancel={handleTransferCancel}
          width="80%"
          okText="确定"
          cancelText="取消"
        >
          <Spin spinning={loading} tip="加载中...">
            <Transfer
              dataSource={traceData} // 数据源
              targetKeys={transferTargetKeys} // 已选择的keys
              onChange={handleTransferChange} // 选择变化回调
              render={renderItem} // 渲染每个选项的显示内容
              titles={['可选对象', '已选对象']} // 左右两侧的标题
              showSearch // 启用搜索功能
              onSearch={handleSearch} // 自定义搜索处理
              searchPlaceholder="搜索公司名称或指标（支持服务端搜索）"
              pagination={{
                pageSize: 50, // 每页显示50条
                showSizeChanger: true,
              }}
              listStyle={{
                width: 300,
                height: 400,
              }}
              footer={(_, { direction }) => renderLoadMoreButton(direction)}
            />
          </Spin>
        </Modal>
      </ConfigProvider>
    </>
  );
};

export default TraceObjectSelector;
