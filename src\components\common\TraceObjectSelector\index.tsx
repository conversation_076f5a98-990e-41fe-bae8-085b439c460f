import React, { useState, useCallback, useEffect } from 'react';
import { Input, Modal, Transfer, ConfigProvider, Table } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { envRequest } from '@/request';

/**
 * 溯源对象数据结构
 */
interface TraceObject {
  key: string; // 唯一标识
  title: string; // 显示名称
  description: string; // 描述信息
  company: string; // 公司名称
  traceIndex: string; // 溯源指标
}

/**
 * 溯源对象选择器组件属性
 */
interface TraceObjectSelectorProps {
  value?: string[]; // 已选择的溯源对象ID数组
  onChange?: (value: string[]) => void; // 选择变化时的回调函数
  disabled?: boolean; // 是否禁用
  isTraceEnabled?: boolean; // 是否开启溯源功能
  type?: 'view' | 'edit' | 'add'; // 组件状态类型
}

/**
 * 溯源对象选择器组件
 * 用于环境质量监测中的溯源对象选择功能
 * 显示为输入框样式，点击打开穿梭框进行选择
 */
const TraceObjectSelector: React.FC<TraceObjectSelectorProps> = ({
  value = [], // 已选择的溯源对象ID数组，默认为空数组
  onChange, // 选择变化时的回调函数
  disabled = false, // 是否禁用，默认为false
  isTraceEnabled = false, // 是否开启溯源功能，默认为false
  type = 'edit', // 组件状态类型，默认为编辑模式
}) => {
  // 穿梭框显示状态
  const [transferVisible, setTransferVisible] = useState<boolean>(false);
  // 穿梭框中已选择的keys，初始化为传入的value
  const [transferTargetKeys, setTransferTargetKeys] = useState<string[]>(value || []);
  // 溯源对象数据
  const [traceData, setTraceData] = useState<TraceObject[]>([]);
  // 数据加载状态
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * 获取溯源对象数据
   * 一次性加载所有数据
   */
  const fetchTraceData = useCallback(async () => {
    try {
      setLoading(true);
      // 调用接口获取污染源企业数据
      const response = await envRequest('/pollution-source/list', {
        method: 'POST',
        data: {
          currentPage: 1,
          pageSize: 1000, // 一次性获取所有数据
          condition: {
            // 可以添加查询条件，比如企业类型等
          },
        },
      });

      if (response && response.list) {
        // 将接口返回的数据转换为穿梭框需要的格式
        const formattedData: TraceObject[] = response.list.map((item: any, index: number) => {
          const company = item.name || item.companyName || item.unitName || `企业${index + 1}`;
          const traceIndex = item.traceIndex || item.indexName || item.indicator || '未知指标';

          return {
            key: item.id || `trace_${index}`,
            title: `${company} - ${traceIndex}`, // 用于搜索和显示
            description: item.industryType || item.type || item.description || '企业',
            company,
            traceIndex,
          };
        });

        // 直接设置数据
        setTraceData(formattedData);
      } else {
        // 如果接口没有数据或失败，使用备用模拟数据
        const fallbackData: TraceObject[] = [
          {
            key: 'comp_001_hg',
            title: '云南安宁化工有限公司 - SO2排放量',
            description: '化工企业',
            company: '云南安宁化工有限公司',
            traceIndex: 'SO2排放量',
          },
          {
            key: 'comp_002_gt',
            title: '安宁钢铁集团有限公司 - NOx排放量',
            description: '钢铁企业',
            company: '安宁钢铁集团有限公司',
            traceIndex: 'NOx排放量',
          },
          {
            key: 'comp_003_dl',
            title: '安宁电力发展有限公司 - 烟尘排放量',
            description: '电力企业',
            company: '安宁电力发展有限公司',
            traceIndex: '烟尘排放量',
          },
          {
            key: 'comp_004_sn',
            title: '云南安宁水泥股份有限公司 - 颗粒物排放量',
            description: '水泥企业',
            company: '云南安宁水泥股份有限公司',
            traceIndex: '颗粒物排放量',
          },
          {
            key: 'comp_005_zz',
            title: '安宁造纸工业有限公司 - COD排放量',
            description: '造纸企业',
            company: '安宁造纸工业有限公司',
            traceIndex: 'COD排放量',
          },
          {
            key: 'comp_006_sh',
            title: '中石化安宁分公司 - VOCs排放量',
            description: '石化企业',
            company: '中石化安宁分公司',
            traceIndex: 'VOCs排放量',
          },
          {
            key: 'comp_007_fz',
            title: '安宁纺织印染有限公司 - 氨氮排放量',
            description: '纺织企业',
            company: '安宁纺织印染有限公司',
            traceIndex: '氨氮排放量',
          },
          {
            key: 'comp_008_sp',
            title: '云南安宁食品加工有限公司 - 总磷排放量',
            description: '食品企业',
            company: '云南安宁食品加工有限公司',
            traceIndex: '总磷排放量',
          },
          {
            key: 'comp_009_yy',
            title: '安宁制药股份有限公司 - 重金属排放量',
            description: '医药企业',
            company: '安宁制药股份有限公司',
            traceIndex: '重金属排放量',
          },
          {
            key: 'comp_010_jc',
            title: '安宁建材集团有限公司 - 粉尘排放量',
            description: '建材企业',
            company: '安宁建材集团有限公司',
            traceIndex: '粉尘排放量',
          },
          {
            key: 'comp_011_jx',
            title: '云南安宁机械制造有限公司 - 噪声排放量',
            description: '机械企业',
            company: '云南安宁机械制造有限公司',
            traceIndex: '噪声排放量',
          },
          {
            key: 'comp_012_dz',
            title: '安宁电子科技有限公司 - 废水排放量',
            description: '电子企业',
            company: '安宁电子科技有限公司',
            traceIndex: '废水排放量',
          },
          {
            key: 'comp_013_qg',
            title: '安宁轻工业发展有限公司 - 固废产生量',
            description: '轻工企业',
            company: '安宁轻工业发展有限公司',
            traceIndex: '固废产生量',
          },
          {
            key: 'comp_014_ys',
            title: '云南安宁有色金属有限公司 - 重金属排放量',
            description: '有色金属企业',
            company: '云南安宁有色金属有限公司',
            traceIndex: '重金属排放量',
          },
          {
            key: 'comp_015_mt',
            title: '安宁煤炭工业有限公司 - 煤尘排放量',
            description: '煤炭企业',
            company: '安宁煤炭工业有限公司',
            traceIndex: '煤尘排放量',
          },
        ];
        setTraceData(fallbackData);
      }
    } catch {
      // 接口调用失败时使用备用数据，静默处理错误
      const fallbackData: TraceObject[] = [
        {
          key: 'comp_001_hg',
          title: '云南安宁化工有限公司 - SO2排放量',
          description: '化工企业',
          company: '云南安宁化工有限公司',
          traceIndex: 'SO2排放量',
        },
        {
          key: 'comp_002_gt',
          title: '安宁钢铁集团有限公司 - NOx排放量',
          description: '钢铁企业',
          company: '安宁钢铁集团有限公司',
          traceIndex: 'NOx排放量',
        },
        {
          key: 'comp_003_dl',
          title: '安宁电力发展有限公司 - 烟尘排放量',
          description: '电力企业',
          company: '安宁电力发展有限公司',
          traceIndex: '烟尘排放量',
        },
        {
          key: 'comp_004_sn',
          title: '云南安宁水泥股份有限公司 - 颗粒物排放量',
          description: '水泥企业',
          company: '云南安宁水泥股份有限公司',
          traceIndex: '颗粒物排放量',
        },
        {
          key: 'comp_005_zz',
          title: '安宁造纸工业有限公司 - COD排放量',
          description: '造纸企业',
          company: '安宁造纸工业有限公司',
          traceIndex: 'COD排放量',
        },
        {
          key: 'comp_006_sh',
          title: '中石化安宁分公司 - VOCs排放量',
          description: '石化企业',
          company: '中石化安宁分公司',
          traceIndex: 'VOCs排放量',
        },
        {
          key: 'comp_007_fz',
          title: '安宁纺织印染有限公司 - 氨氮排放量',
          description: '纺织企业',
          company: '安宁纺织印染有限公司',
          traceIndex: '氨氮排放量',
        },
        {
          key: 'comp_008_sp',
          title: '云南安宁食品加工有限公司 - 总磷排放量',
          description: '食品企业',
          company: '云南安宁食品加工有限公司',
          traceIndex: '总磷排放量',
        },
        {
          key: 'comp_009_yy',
          title: '安宁制药股份有限公司 - 重金属排放量',
          description: '医药企业',
          company: '安宁制药股份有限公司',
          traceIndex: '重金属排放量',
        },
        {
          key: 'comp_010_jc',
          title: '安宁建材集团有限公司 - 粉尘排放量',
          description: '建材企业',
          company: '安宁建材集团有限公司',
          traceIndex: '粉尘排放量',
        },
        {
          key: 'comp_011_jx',
          title: '云南安宁机械制造有限公司 - 噪声排放量',
          description: '机械企业',
          company: '云南安宁机械制造有限公司',
          traceIndex: '噪声排放量',
        },
        {
          key: 'comp_012_dz',
          title: '安宁电子科技有限公司 - 废水排放量',
          description: '电子企业',
          company: '安宁电子科技有限公司',
          traceIndex: '废水排放量',
        },
        {
          key: 'comp_013_qg',
          title: '安宁轻工业发展有限公司 - 固废产生量',
          description: '轻工企业',
          company: '安宁轻工业发展有限公司',
          traceIndex: '固废产生量',
        },
        {
          key: 'comp_014_ys',
          title: '云南安宁有色金属有限公司 - 重金属排放量',
          description: '有色金属企业',
          company: '云南安宁有色金属有限公司',
          traceIndex: '重金属排放量',
        },
        {
          key: 'comp_015_mt',
          title: '安宁煤炭工业有限公司 - 煤尘排放量',
          description: '煤炭企业',
          company: '安宁煤炭工业有限公司',
          traceIndex: '煤尘排放量',
        },
      ];
      setTraceData(fallbackData);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 表格列配置
   */
  const tableColumns = [
    {
      dataIndex: 'company',
      title: '公司名称',
      width: '60%',
      ellipsis: true,
    },
    {
      dataIndex: 'traceIndex',
      title: '溯源指标',
      width: '40%',
      ellipsis: true,
    },
  ];

  /**
   * 自定义渲染函数 - 使用 Table 组件
   */
  const renderTransferList = useCallback((props: any) => {
    const { filteredItems, onItemSelectAll, onItemSelect, selectedKeys } = props;

    const rowSelection = {
      selectedRowKeys: selectedKeys,
      onSelectAll: (selected: boolean, _selectedRows: any[], changeRows: any[]) => {
        const treeSelectedKeys = changeRows.map(({ key }) => key);
        onItemSelectAll(treeSelectedKeys, selected);
      },
      onSelect: ({ key }: any, selected: boolean) => {
        onItemSelect(key, selected);
      },
    };

    return (
      <div style={{ height: '400px', overflow: 'hidden' }}>
        <Table
          rowSelection={rowSelection}
          columns={tableColumns}
          dataSource={filteredItems}
          size="small"
          pagination={false} // 禁用 Table 自带分页，使用 Transfer 的分页
          scroll={{
            y: 320, // 减少高度，为表头留出空间
            scrollToFirstRowOnChange: true, // 分页时滚动到顶部
          }}
          style={{
            height: '100%',
          }}
          showHeader={true}
          bordered={false}
          rowKey="key" // 指定行的唯一标识
        />
      </div>
    );
  }, [tableColumns]);

  // 组件挂载时获取数据
  useEffect(() => {
    fetchTraceData();
  }, [fetchTraceData]);

  /**
   * 打开穿梭框
   */
  const openModal = useCallback(() => {
    if (disabled || type === 'view' || !isTraceEnabled) return;
    setTransferTargetKeys(value || []);
    setTransferVisible(true);
  }, [disabled, type, isTraceEnabled, value]);

  /**
   * 穿梭框确认操作
   */
  const handleTransferOk = useCallback(() => {
    if (onChange) {
      onChange(transferTargetKeys);
    }
    setTransferVisible(false);
  }, [onChange, transferTargetKeys]);

  /**
   * 穿梭框取消操作
   */
  const handleTransferCancel = useCallback(() => {
    setTransferTargetKeys(value || []);
    setTransferVisible(false);
  }, [value]);

  /**
   * 穿梭框数据变化处理
   */
  const handleTransferChange = useCallback((targetKeys: string[]) => {
    setTransferTargetKeys(targetKeys);
  }, []);

  // 如果未开启溯源，显示 "-"
  if (!isTraceEnabled) {
    return (
      <Input
        value="-"
        readOnly
        disabled
        style={{
          cursor: 'default',
          backgroundColor: '#f5f5f5',
        }}
      />
    );
  }

  // 计算已选择的对象数量
  const count = value?.length || 0;
  // 显示文本：有选择时显示数量，无选择时显示"请选择"
  const displayText = count > 0 ? count.toString() : '请选择';

  return (
    <>
      {/* 输入框样式的选择器 */}
      <Input
        value={displayText}
        readOnly // 只读，不允许直接输入
        placeholder="请选择"
        style={{
          cursor: disabled || type === 'view' ? 'default' : 'pointer',
          backgroundColor: disabled || type === 'view' ? '#f5f5f5' : '#fff',
        }}
        onClick={openModal}
        disabled={disabled || type === 'view'}
      />

      {/* 溯源对象选择穿梭框 */}
      <ConfigProvider locale={zhCN}>
        <Modal
          title="选择溯源对象"
          visible={transferVisible}
          onOk={handleTransferOk}
          onCancel={handleTransferCancel}
          okText="确定"
          width={1200}
          cancelText="取消"
        >
          <div style={{ padding: '20px' }}>
            <Transfer
              dataSource={traceData}
              targetKeys={transferTargetKeys}
              onChange={handleTransferChange}
              titles={['可选对象', '已选对象']}
              showSearch
              showSelectAll
              oneWay={false}
              pagination={{
                pageSize: 20, // 每页显示20条数据
                showSizeChanger: true,
              }}
              listStyle={{
                width: 520,
                height: 500,
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
              }}
              style={{
                width: '100%',
              }}
              locale={{
                itemUnit: '项',
                itemsUnit: '项',
                searchPlaceholder: '搜索公司名称或指标',
                notFoundContent: '暂无数据',
              }}
            >
              {renderTransferList}
            </Transfer>
          </div>
        </Modal>
      </ConfigProvider>
    </>
  );
};

export default TraceObjectSelector;
