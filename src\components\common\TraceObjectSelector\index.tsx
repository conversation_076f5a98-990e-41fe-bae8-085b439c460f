import React, { useState, useCallback, useEffect } from 'react';
import { Input, Modal, Transfer, ConfigProvider, Spin } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { envRequest } from '@/request';

/**
 * 溯源对象数据结构
 */
interface TraceObject {
  key: string; // 唯一标识
  title: string; // 显示名称
  description: string; // 描述信息
}

/**
 * 溯源对象选择器组件属性
 */
interface TraceObjectSelectorProps {
  value?: string[]; // 已选择的溯源对象ID数组
  onChange?: (value: string[]) => void; // 选择变化时的回调函数
  disabled?: boolean; // 是否禁用
  type?: 'view' | 'edit' | 'add'; // 组件状态类型
}

/**
 * 溯源对象选择器组件
 * 用于环境质量监测中的溯源对象选择功能
 * 显示为输入框样式，点击打开穿梭框进行选择
 */
const TraceObjectSelector: React.FC<TraceObjectSelectorProps> = ({
  value = [], // 已选择的溯源对象ID数组，默认为空数组
  onChange, // 选择变化时的回调函数
  disabled = false, // 是否禁用，默认为false
  type = 'edit', // 组件状态类型，默认为编辑模式
}) => {
  // 穿梭框显示状态
  const [transferVisible, setTransferVisible] = useState<boolean>(false);
  // 穿梭框中已选择的keys，初始化为传入的value
  const [transferTargetKeys, setTransferTargetKeys] = useState<string[]>(value || []);
  // 溯源对象数据
  const [traceData, setTraceData] = useState<TraceObject[]>([]);
  // 数据加载状态
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * 获取溯源对象数据
   * 从接口获取污染源企业列表
   */
  const fetchTraceData = useCallback(async () => {
    try {
      setLoading(true);
      // 调用接口获取污染源企业数据
      // 这里使用一个通用的企业查询接口，您可以根据实际情况调整
      const response = await envRequest('/pollution-source/list', {
        method: 'POST',
        data: {
          currentPage: 1,
          pageSize: 50, // 获取更多数据
          condition: {
            // 可以添加查询条件，比如企业类型等
          },
        },
      });

      if (response && response.list) {
        // 将接口返回的数据转换为穿梭框需要的格式
        const formattedData: TraceObject[] = response.list.map((item: any, index: number) => ({
          key: item.id || `trace_${index}`,
          title: item.name || item.companyName || item.unitName || `企业${index + 1}`,
          description: item.industryType || item.type || item.description || '企业',
        }));
        setTraceData(formattedData);
      } else {
        // 如果接口没有数据或失败，使用备用模拟数据
        const fallbackData: TraceObject[] = [
          { key: 'comp_001_hg', title: '云南安宁化工有限公司', description: '化工企业' },
          { key: 'comp_002_gt', title: '安宁钢铁集团有限公司', description: '钢铁企业' },
          { key: 'comp_003_dl', title: '安宁电力发展有限公司', description: '电力企业' },
          { key: 'comp_004_sn', title: '云南安宁水泥股份有限公司', description: '水泥企业' },
          { key: 'comp_005_zz', title: '安宁造纸工业有限公司', description: '造纸企业' },
          { key: 'comp_006_sh', title: '中石化安宁分公司', description: '石化企业' },
          { key: 'comp_007_fz', title: '安宁纺织印染有限公司', description: '纺织企业' },
          { key: 'comp_008_sp', title: '云南安宁食品加工有限公司', description: '食品企业' },
          { key: 'comp_009_yy', title: '安宁制药股份有限公司', description: '医药企业' },
          { key: 'comp_010_jc', title: '安宁建材集团有限公司', description: '建材企业' },
          { key: 'comp_011_jx', title: '云南安宁机械制造有限公司', description: '机械企业' },
          { key: 'comp_012_dz', title: '安宁电子科技有限公司', description: '电子企业' },
          { key: 'comp_013_qg', title: '安宁轻工业发展有限公司', description: '轻工企业' },
          { key: 'comp_014_ys', title: '云南安宁有色金属有限公司', description: '有色金属企业' },
          { key: 'comp_015_mt', title: '安宁煤炭工业有限公司', description: '煤炭企业' },
        ];
        setTraceData(fallbackData);
      }
    } catch {
      // 接口调用失败时使用备用数据，静默处理错误
      const fallbackData: TraceObject[] = [
        { key: 'comp_001_hg', title: '云南安宁化工有限公司', description: '化工企业' },
        { key: 'comp_002_gt', title: '安宁钢铁集团有限公司', description: '钢铁企业' },
        { key: 'comp_003_dl', title: '安宁电力发展有限公司', description: '电力企业' },
        { key: 'comp_004_sn', title: '云南安宁水泥股份有限公司', description: '水泥企业' },
        { key: 'comp_005_zz', title: '安宁造纸工业有限公司', description: '造纸企业' },
        { key: 'comp_006_sh', title: '中石化安宁分公司', description: '石化企业' },
        { key: 'comp_007_fz', title: '安宁纺织印染有限公司', description: '纺织企业' },
        { key: 'comp_008_sp', title: '云南安宁食品加工有限公司', description: '食品企业' },
        { key: 'comp_009_yy', title: '安宁制药股份有限公司', description: '医药企业' },
        { key: 'comp_010_jc', title: '安宁建材集团有限公司', description: '建材企业' },
        { key: 'comp_011_jx', title: '云南安宁机械制造有限公司', description: '机械企业' },
        { key: 'comp_012_dz', title: '安宁电子科技有限公司', description: '电子企业' },
        { key: 'comp_013_qg', title: '安宁轻工业发展有限公司', description: '轻工企业' },
        { key: 'comp_014_ys', title: '云南安宁有色金属有限公司', description: '有色金属企业' },
        { key: 'comp_015_mt', title: '安宁煤炭工业有限公司', description: '煤炭企业' },
      ];
      setTraceData(fallbackData);
    } finally {
      setLoading(false);
    }
  }, []);

  // 组件挂载时获取数据
  useEffect(() => {
    fetchTraceData();
  }, [fetchTraceData]);

  /**
   * 打开穿梭框
   * 在禁用或查看模式下不允许打开
   */
  const openModal = useCallback(() => {
    if (disabled || type === 'view') return;
    // 确保打开时使用最新的值，避免状态不同步
    setTransferTargetKeys(value || []);
    setTransferVisible(true);
  }, [disabled, type, value]);

  /**
   * 穿梭框确认操作
   * 将选中的数据通过onChange回调传递给父组件
   */
  const handleTransferOk = useCallback(() => {
    if (onChange) {
      onChange(transferTargetKeys);
    }
    setTransferVisible(false);
  }, [onChange, transferTargetKeys]);

  /**
   * 穿梭框取消操作
   * 恢复到原始值，不保存任何更改
   */
  const handleTransferCancel = useCallback(() => {
    setTransferTargetKeys(value || []);
    setTransferVisible(false);
  }, [value]);

  /**
   * 穿梭框数据变化处理
   * 实时更新选中的keys
   */
  const handleTransferChange = useCallback((targetKeys: string[]) => {
    setTransferTargetKeys(targetKeys);
  }, []);

  /**
   * 渲染穿梭框选项
   * 使用useCallback避免重复渲染
   */
  const renderItem = useCallback((item: TraceObject) => {
    return item.title;
  }, []);

  // 计算已选择的对象数量
  const count = value?.length || 0;
  // 显示文本：有选择时显示数量，无选择时显示"请选择"
  const displayText = count > 0 ? count.toString() : '请选择';

  return (
    <>
      {/* 输入框样式的选择器 */}
      <Input
        value={displayText}
        readOnly // 只读，不允许直接输入
        placeholder="请选择"
        style={{
          cursor: disabled || type === 'view' ? 'default' : 'pointer',
          backgroundColor: disabled || type === 'view' ? '#f5f5f5' : '#fff',
        }}
        onClick={openModal}
        disabled={disabled || type === 'view'}
      />

      {/* 溯源对象选择穿梭框 */}
      <ConfigProvider locale={zhCN}>
        <Modal
          title="选择溯源对象"
          open={transferVisible}
          onOk={handleTransferOk}
          onCancel={handleTransferCancel}
          width={800}
          okText="确定"
          cancelText="取消"
        >
          <Spin spinning={loading} tip="加载中...">
            <Transfer
              dataSource={traceData} // 数据源
              targetKeys={transferTargetKeys} // 已选择的keys
              onChange={handleTransferChange} // 选择变化回调
              render={renderItem} // 渲染每个选项的显示内容
              titles={['可选对象', '已选对象']} // 左右两侧的标题
              showSearch // 启用搜索功能
              listStyle={{
                width: 300,
                height: 400,
              }}
            />
          </Spin>
        </Modal>
      </ConfigProvider>
    </>
  );
};

export default TraceObjectSelector;
