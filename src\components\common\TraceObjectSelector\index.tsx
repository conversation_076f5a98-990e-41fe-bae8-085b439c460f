import React, { useState, useEffect } from 'react';
import { Input, Modal, Transfer } from 'antd';

/**
 * 溯源对象数据结构
 */
interface TraceObject {
  key: string; // 唯一标识
  title: string; // 显示名称
  description: string; // 描述信息
}

/**
 * 溯源对象选择器组件属性
 */
interface TraceObjectSelectorProps {
  value?: string[]; // 已选择的溯源对象ID数组
  onChange?: (value: string[]) => void; // 选择变化时的回调函数
  disabled?: boolean; // 是否禁用
  type?: 'view' | 'edit' | 'add'; // 组件状态类型
}

/**
 * 溯源对象选择器组件
 * 用于环境质量监测中的溯源对象选择功能
 * 显示为输入框样式，点击打开穿梭框进行选择
 */
const TraceObjectSelector: React.FC<TraceObjectSelectorProps> = ({
  value = [], // 已选择的溯源对象ID数组，默认为空数组
  onChange, // 选择变化时的回调函数
  disabled = false, // 是否禁用，默认为false
  type = 'edit', // 组件状态类型，默认为编辑模式
}) => {
  // 穿梭框显示状态
  const [transferVisible, setTransferVisible] = useState<boolean>(false);
  // 穿梭框中已选择的keys，初始化为传入的value
  const [transferTargetKeys, setTransferTargetKeys] = useState<string[]>(value || []);

  // 模拟溯源对象数据 - 实际项目中应该从API获取
  const mockTraceData: TraceObject[] = [
    { key: '1', title: '污染源企业A', description: '化工企业' },
    { key: '2', title: '污染源企业B', description: '钢铁企业' },
    { key: '3', title: '污染源企业C', description: '电力企业' },
    { key: '4', title: '污染源企业D', description: '水泥企业' },
    { key: '5', title: '污染源企业E', description: '造纸企业' },
    { key: '6', title: '污染源企业F', description: '石化企业' },
    { key: '7', title: '污染源企业G', description: '纺织企业' },
    { key: '8', title: '污染源企业H', description: '食品企业' },
  ];

  // 监听value变化，同步更新穿梭框的选中状态
  useEffect(() => {
    setTransferTargetKeys(value || []);
  }, [value]);

  /**
   * 打开穿梭框
   * 在禁用或查看模式下不允许打开
   */
  const openModal = () => {
    if (disabled || type === 'view') return;
    // 确保打开时使用最新的值，避免状态不同步
    setTransferTargetKeys(value || []);
    setTransferVisible(true);
  };

  /**
   * 穿梭框确认操作
   * 将选中的数据通过onChange回调传递给父组件
   */
  const handleTransferOk = () => {
    if (onChange) {
      onChange(transferTargetKeys);
    }
    setTransferVisible(false);
  };

  /**
   * 穿梭框取消操作
   * 恢复到原始值，不保存任何更改
   */
  const handleTransferCancel = () => {
    setTransferTargetKeys(value || []);
    setTransferVisible(false);
  };

  /**
   * 穿梭框数据变化处理
   * 实时更新选中的keys
   */
  const handleTransferChange = (targetKeys: string[]) => {
    setTransferTargetKeys(targetKeys);
  };

  // 计算已选择的对象数量
  const count = value?.length || 0;
  // 显示文本：有选择时显示数量，无选择时显示"请选择"
  const displayText = count > 0 ? count.toString() : '请选择';

  return (
    <>
      {/* 输入框样式的选择器 */}
      <Input
        value={displayText}
        readOnly // 只读，不允许直接输入
        placeholder="请选择"
        style={{
          cursor: disabled || type === 'view' ? 'default' : 'pointer',
          backgroundColor: disabled || type === 'view' ? '#f5f5f5' : '#fff',
        }}
        onClick={openModal}
        disabled={disabled || type === 'view'}
      />

      {/* 溯源对象选择穿梭框 */}
      <Modal
        title="选择溯源对象"
        open={transferVisible}
        onOk={handleTransferOk}
        onCancel={handleTransferCancel}
        width={800}
        okText="确定"
        cancelText="取消"
      >
        <Transfer
          dataSource={mockTraceData} // 数据源
          targetKeys={transferTargetKeys} // 已选择的keys
          onChange={handleTransferChange} // 选择变化回调
          render={(item) => item.title} // 渲染每个选项的显示内容
          titles={['可选对象', '已选对象']} // 左右两侧的标题
          showSearch // 启用搜索功能
          listStyle={{
            width: 300,
            height: 400,
          }}
        />
      </Modal>
    </>
  );
};

export default TraceObjectSelector;
