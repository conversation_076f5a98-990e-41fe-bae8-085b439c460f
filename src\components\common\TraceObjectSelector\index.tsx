import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Input, Modal, ConfigProvider, Button, Row, Col } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import zhCN from 'antd/lib/locale/zh_CN';
import { envRequest } from '@/request';
import { YTHList } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';

/**
 * 溯源对象数据结构
 */
interface TraceObject {
  key: string; // 唯一标识
  title: string; // 显示名称
  description: string; // 描述信息
  company: string; // 公司名称
  traceIndex: string; // 溯源指标
}

/**
 * 溯源对象选择器组件属性
 */
interface TraceObjectSelectorProps {
  value?: string[]; // 已选择的溯源对象ID数组
  onChange?: (value: string[]) => void; // 选择变化时的回调函数
  disabled?: boolean; // 是否禁用
  isTraceEnabled?: boolean; // 是否开启溯源功能
  type?: 'view' | 'edit' | 'add'; // 组件状态类型
}

/**
 * 溯源对象选择器组件
 * 用于环境质量监测中的溯源对象选择功能
 * 显示为输入框样式，点击打开穿梭框进行选择
 */
const TraceObjectSelector: React.FC<TraceObjectSelectorProps> = ({
  value = [], // 已选择的溯源对象ID数组，默认为空数组
  onChange, // 选择变化时的回调函数
  disabled = false, // 是否禁用，默认为false
  isTraceEnabled = false, // 是否开启溯源功能，默认为false
  type = 'edit', // 组件状态类型，默认为编辑模式
}) => {

  // 溯源对象数据
  const [traceData, setTraceData] = useState<TraceObject[]>([]);
  // 已选择的数据
  const [selectedData, setSelectedData] = useState<TraceObject[]>([]);
  // 数据加载状态
  const [loading, setLoading] = useState<boolean>(false);
  // 左侧列表选中的行
  const [leftSelectedRows, setLeftSelectedRows] = useState<string[]>([]);
  // 右侧列表选中的行
  const [rightSelectedRows, setRightSelectedRows] = useState<string[]>([]);
  // 左侧列表引用
  const leftListRef = useRef<ActionType>();
  // 右侧列表引用
  const rightListRef = useRef<ActionType>();
  // 左侧列表 action
  const leftListAction = YTHList.createAction();
  // 右侧列表 action
  const rightListAction = YTHList.createAction();
  // 模态框状态
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  /**
   * 获取溯源对象数据
   * 一次性加载所有数据
   */
  const fetchTraceData = useCallback(async () => {
    try {
      setLoading(true);
      // 调用接口获取污染源企业数据
      const response = await envRequest('/pollution-source/list', {
        method: 'POST',
        data: {
          currentPage: 1,
          pageSize: 1000, // 一次性获取所有数据
          condition: {
            // 可以添加查询条件，比如企业类型等
          },
        },
      });

      if (response && response.list) {
        // 将接口返回的数据转换为穿梭框需要的格式
        const formattedData: TraceObject[] = response.list.map((item: any, index: number) => {
          const company = item.name || item.companyName || item.unitName || `企业${index + 1}`;
          const traceIndex = item.traceIndex || item.indexName || item.indicator || '未知指标';

          return {
            key: item.id || `trace_${index}`,
            title: `${company} - ${traceIndex}`, // 用于搜索和显示
            description: item.industryType || item.type || item.description || '企业',
            company,
            traceIndex,
          };
        });

        // 直接设置数据
        setTraceData(formattedData);
      } else {
        // 如果接口没有数据或失败，使用备用模拟数据
        const fallbackData: TraceObject[] = [
          {
            key: 'comp_001_hg',
            title: '云南安宁化工有限公司 - SO2排放量',
            description: '化工企业',
            company: '云南安宁化工有限公司',
            traceIndex: 'SO2排放量',
          },
          {
            key: 'comp_002_gt',
            title: '安宁钢铁集团有限公司 - NOx排放量',
            description: '钢铁企业',
            company: '安宁钢铁集团有限公司',
            traceIndex: 'NOx排放量',
          },
          {
            key: 'comp_003_dl',
            title: '安宁电力发展有限公司 - 烟尘排放量',
            description: '电力企业',
            company: '安宁电力发展有限公司',
            traceIndex: '烟尘排放量',
          },
          {
            key: 'comp_004_sn',
            title: '云南安宁水泥股份有限公司 - 颗粒物排放量',
            description: '水泥企业',
            company: '云南安宁水泥股份有限公司',
            traceIndex: '颗粒物排放量',
          },
          {
            key: 'comp_005_zz',
            title: '安宁造纸工业有限公司 - COD排放量',
            description: '造纸企业',
            company: '安宁造纸工业有限公司',
            traceIndex: 'COD排放量',
          },
          {
            key: 'comp_006_sh',
            title: '中石化安宁分公司 - VOCs排放量',
            description: '石化企业',
            company: '中石化安宁分公司',
            traceIndex: 'VOCs排放量',
          },
          {
            key: 'comp_007_fz',
            title: '安宁纺织印染有限公司 - 氨氮排放量',
            description: '纺织企业',
            company: '安宁纺织印染有限公司',
            traceIndex: '氨氮排放量',
          },
          {
            key: 'comp_008_sp',
            title: '云南安宁食品加工有限公司 - 总磷排放量',
            description: '食品企业',
            company: '云南安宁食品加工有限公司',
            traceIndex: '总磷排放量',
          },
          {
            key: 'comp_009_yy',
            title: '安宁制药股份有限公司 - 重金属排放量',
            description: '医药企业',
            company: '安宁制药股份有限公司',
            traceIndex: '重金属排放量',
          },
          {
            key: 'comp_010_jc',
            title: '安宁建材集团有限公司 - 粉尘排放量',
            description: '建材企业',
            company: '安宁建材集团有限公司',
            traceIndex: '粉尘排放量',
          },
          {
            key: 'comp_011_jx',
            title: '云南安宁机械制造有限公司 - 噪声排放量',
            description: '机械企业',
            company: '云南安宁机械制造有限公司',
            traceIndex: '噪声排放量',
          },
          {
            key: 'comp_012_dz',
            title: '安宁电子科技有限公司 - 废水排放量',
            description: '电子企业',
            company: '安宁电子科技有限公司',
            traceIndex: '废水排放量',
          },
          {
            key: 'comp_013_qg',
            title: '安宁轻工业发展有限公司 - 固废产生量',
            description: '轻工企业',
            company: '安宁轻工业发展有限公司',
            traceIndex: '固废产生量',
          },
          {
            key: 'comp_014_ys',
            title: '云南安宁有色金属有限公司 - 重金属排放量',
            description: '有色金属企业',
            company: '云南安宁有色金属有限公司',
            traceIndex: '重金属排放量',
          },
          {
            key: 'comp_015_mt',
            title: '安宁煤炭工业有限公司 - 煤尘排放量',
            description: '煤炭企业',
            company: '安宁煤炭工业有限公司',
            traceIndex: '煤尘排放量',
          },
        ];
        setTraceData(fallbackData);
      }
    } catch {
      // 接口调用失败时使用备用数据，静默处理错误
      const fallbackData: TraceObject[] = [
        {
          key: 'comp_001_hg',
          title: '云南安宁化工有限公司 - SO2排放量',
          description: '化工企业',
          company: '云南安宁化工有限公司',
          traceIndex: 'SO2排放量',
        },
        {
          key: 'comp_002_gt',
          title: '安宁钢铁集团有限公司 - NOx排放量',
          description: '钢铁企业',
          company: '安宁钢铁集团有限公司',
          traceIndex: 'NOx排放量',
        },
        {
          key: 'comp_003_dl',
          title: '安宁电力发展有限公司 - 烟尘排放量',
          description: '电力企业',
          company: '安宁电力发展有限公司',
          traceIndex: '烟尘排放量',
        },
        {
          key: 'comp_004_sn',
          title: '云南安宁水泥股份有限公司 - 颗粒物排放量',
          description: '水泥企业',
          company: '云南安宁水泥股份有限公司',
          traceIndex: '颗粒物排放量',
        },
        {
          key: 'comp_005_zz',
          title: '安宁造纸工业有限公司 - COD排放量',
          description: '造纸企业',
          company: '安宁造纸工业有限公司',
          traceIndex: 'COD排放量',
        },
        {
          key: 'comp_006_sh',
          title: '中石化安宁分公司 - VOCs排放量',
          description: '石化企业',
          company: '中石化安宁分公司',
          traceIndex: 'VOCs排放量',
        },
        {
          key: 'comp_007_fz',
          title: '安宁纺织印染有限公司 - 氨氮排放量',
          description: '纺织企业',
          company: '安宁纺织印染有限公司',
          traceIndex: '氨氮排放量',
        },
        {
          key: 'comp_008_sp',
          title: '云南安宁食品加工有限公司 - 总磷排放量',
          description: '食品企业',
          company: '云南安宁食品加工有限公司',
          traceIndex: '总磷排放量',
        },
        {
          key: 'comp_009_yy',
          title: '安宁制药股份有限公司 - 重金属排放量',
          description: '医药企业',
          company: '安宁制药股份有限公司',
          traceIndex: '重金属排放量',
        },
        {
          key: 'comp_010_jc',
          title: '安宁建材集团有限公司 - 粉尘排放量',
          description: '建材企业',
          company: '安宁建材集团有限公司',
          traceIndex: '粉尘排放量',
        },
        {
          key: 'comp_011_jx',
          title: '云南安宁机械制造有限公司 - 噪声排放量',
          description: '机械企业',
          company: '云南安宁机械制造有限公司',
          traceIndex: '噪声排放量',
        },
        {
          key: 'comp_012_dz',
          title: '安宁电子科技有限公司 - 废水排放量',
          description: '电子企业',
          company: '安宁电子科技有限公司',
          traceIndex: '废水排放量',
        },
        {
          key: 'comp_013_qg',
          title: '安宁轻工业发展有限公司 - 固废产生量',
          description: '轻工企业',
          company: '安宁轻工业发展有限公司',
          traceIndex: '固废产生量',
        },
        {
          key: 'comp_014_ys',
          title: '云南安宁有色金属有限公司 - 重金属排放量',
          description: '有色金属企业',
          company: '云南安宁有色金属有限公司',
          traceIndex: '重金属排放量',
        },
        {
          key: 'comp_015_mt',
          title: '安宁煤炭工业有限公司 - 煤尘排放量',
          description: '煤炭企业',
          company: '安宁煤炭工业有限公司',
          traceIndex: '煤尘排放量',
        },
      ];
      setTraceData(fallbackData);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 左侧列表列配置
   */
  const leftColumns: IYTHColumnProps[] = [
    {
      dataIndex: 'company',
      title: '公司名称',
      width: 200,
      query: true,
      display: true,
    },
    {
      dataIndex: 'traceIndex',
      title: '溯源指标',
      width: 150,
      query: true,
      display: true,
    },
    {
      dataIndex: 'description',
      title: '企业类型',
      width: 120,
      query: false,
      display: true,
    },
  ];

  /**
   * 右侧列表列配置
   */
  const rightColumns: IYTHColumnProps[] = [
    {
      dataIndex: 'company',
      title: '公司名称',
      width: 200,
      display: true,
    },
    {
      dataIndex: 'traceIndex',
      title: '溯源指标',
      width: 150,
      display: true,
    },
  ];

  /**
   * 添加到右侧（选择）
   */
  const addToSelected = useCallback(() => {
    if (leftSelectedRows.length === 0) return;

    const itemsToAdd = traceData.filter(item => leftSelectedRows.includes(item.key));
    const newSelectedData = [...selectedData, ...itemsToAdd];
    setSelectedData(newSelectedData);

    // 更新父组件的值
    const newValue = newSelectedData.map(item => item.key);
    onChange?.(newValue);

    // 清空左侧选择
    setLeftSelectedRows([]);
  }, [leftSelectedRows, traceData, selectedData, onChange]);

  /**
   * 从右侧移除（取消选择）
   */
  const removeFromSelected = useCallback(() => {
    if (rightSelectedRows.length === 0) return;

    const newSelectedData = selectedData.filter(item => !rightSelectedRows.includes(item.key));
    setSelectedData(newSelectedData);

    // 更新父组件的值
    const newValue = newSelectedData.map(item => item.key);
    onChange?.(newValue);

    // 清空右侧选择
    setRightSelectedRows([]);
  }, [rightSelectedRows, selectedData, onChange]);

  // 组件挂载时获取数据
  useEffect(() => {
    fetchTraceData(); // 一次性加载所有数据
  }, [fetchTraceData]);

  // 根据 value 初始化已选择的数据
  useEffect(() => {
    if (value && value.length > 0 && traceData.length > 0) {
      const selected = traceData.filter(item => value.includes(item.key));
      setSelectedData(selected);
    } else {
      setSelectedData([]);
    }
  }, [value, traceData]);

  /**
   * 打开选择器
   * 在禁用、查看模式或未开启溯源时不允许打开
   */
  const openModal = useCallback(() => {
    if (disabled || type === 'view' || !isTraceEnabled) return;
    setModalVisible(true);
  }, [disabled, type, isTraceEnabled]);

  /**
   * 确认选择
   */
  const handleOk = useCallback(() => {
    setModalVisible(false);
  }, []);

  /**
   * 取消选择
   */
  const handleCancel = useCallback(() => {
    // 恢复到原始值
    if (value && value.length > 0 && traceData.length > 0) {
      const selected = traceData.filter(item => value.includes(item.key));
      setSelectedData(selected);
    } else {
      setSelectedData([]);
    }
    setLeftSelectedRows([]);
    setRightSelectedRows([]);
    setModalVisible(false);
  }, [value, traceData]);

  // 如果未开启溯源，显示 "-"
  if (!isTraceEnabled) {
    return (
      <Input
        value="-"
        readOnly
        disabled
        style={{
          cursor: 'default',
          backgroundColor: '#f5f5f5',
        }}
      />
    );
  }

  // 计算已选择的对象数量
  const count = value?.length || 0;
  // 显示文本：有选择时显示数量，无选择时显示"请选择"
  const displayText = count > 0 ? count.toString() : '请选择';

  return (
    <>
      {/* 输入框样式的选择器 */}
      <Input
        value={displayText}
        readOnly // 只读，不允许直接输入
        placeholder="请选择"
        style={{
          cursor: disabled || type === 'view' ? 'default' : 'pointer',
          backgroundColor: disabled || type === 'view' ? '#f5f5f5' : '#fff',
        }}
        onClick={openModal}
        disabled={disabled || type === 'view'}
      />

      {/* 溯源对象选择穿梭框 */}
      <ConfigProvider locale={zhCN}>
        <Modal
          title="选择溯源对象"
          open={modalVisible}
          onOk={handleOk}
          onCancel={handleCancel}
          width="90%"
          okText="确定"
          cancelText="取消"
        >
          <Row gutter={16} style={{ height: '600px' }}>
            {/* 左侧：可选对象列表 */}
            <Col span={10}>
              <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', height: '100%' }}>
                <div style={{ padding: '12px', borderBottom: '1px solid #d9d9d9', background: '#fafafa' }}>
                  <strong>可选对象</strong>
                </div>
                <div style={{ height: 'calc(100% - 49px)' }}>
                  <YTHList
                    code="traceObjectLeft"
                    action={leftListAction}
                    actionRef={leftListRef}
                    columns={leftColumns}
                    showRowSelection={true}
                    rowSelection={{
                      selectedRowKeys: leftSelectedRows,
                      onChange: (selectedRowKeys) => {
                        setLeftSelectedRows(selectedRowKeys as string[]);
                      },
                    }}
                    request={async (filter, pagination) => {
                      // 过滤掉已选择的数据
                      const availableData = traceData.filter(
                        item => !selectedData.some(selected => selected.key === item.key)
                      );

                      // 应用搜索过滤
                      let filteredData = availableData;
                      if (filter.company) {
                        filteredData = filteredData.filter(item =>
                          item.company.includes(filter.company)
                        );
                      }
                      if (filter.traceIndex) {
                        filteredData = filteredData.filter(item =>
                          item.traceIndex.includes(filter.traceIndex)
                        );
                      }

                      // 分页处理
                      const start = (pagination.current - 1) * pagination.pageSize;
                      const end = start + pagination.pageSize;
                      const pageData = filteredData.slice(start, end);

                      return {
                        data: pageData,
                        total: filteredData.length,
                        success: true,
                      };
                    }}
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: false,
                    }}
                    search={false}
                    toolBarRender={false}
                  />
                </div>
              </div>
            </Col>

            {/* 中间：操作按钮 */}
            <Col span={4} style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
              <Button
                type="primary"
                icon={<RightOutlined />}
                onClick={addToSelected}
                disabled={leftSelectedRows.length === 0}
                style={{ marginBottom: '16px', width: '80px' }}
              >
                添加
              </Button>
              <Button
                icon={<LeftOutlined />}
                onClick={removeFromSelected}
                disabled={rightSelectedRows.length === 0}
                style={{ width: '80px' }}
              >
                移除
              </Button>
            </Col>

            {/* 右侧：已选对象列表 */}
            <Col span={10}>
              <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', height: '100%' }}>
                <div style={{ padding: '12px', borderBottom: '1px solid #d9d9d9', background: '#fafafa' }}>
                  <strong>已选对象 ({selectedData.length})</strong>
                </div>
                <div style={{ height: 'calc(100% - 49px)' }}>
                  <YTHList
                    code="traceObjectRight"
                    action={rightListAction}
                    actionRef={rightListRef}
                    columns={rightColumns}
                    showRowSelection={true}
                    rowSelection={{
                      selectedRowKeys: rightSelectedRows,
                      onChange: (selectedRowKeys) => {
                        setRightSelectedRows(selectedRowKeys as string[]);
                      },
                    }}
                    request={async (filter, pagination) => {
                      // 分页处理
                      const start = (pagination.current - 1) * pagination.pageSize;
                      const end = start + pagination.pageSize;
                      const pageData = selectedData.slice(start, end);

                      return {
                        data: pageData,
                        total: selectedData.length,
                        success: true,
                      };
                    }}
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: false,
                    }}
                    search={false}
                    toolBarRender={false}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Modal>
      </ConfigProvider>
    </>
  );
};

export default TraceObjectSelector;
