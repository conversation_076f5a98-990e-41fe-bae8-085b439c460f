# 溯源对象选择器组件

## 功能描述

这是一个用于选择溯源对象的组件，主要用于环境质量监测中的溯源功能。当监测指标开启溯源时，可以通过此组件选择相关的污染源企业。

## 主要特性

1. **输入框样式**: 使用输入框样式显示，界面更统一
2. **数字显示**: 只显示选择的对象数量，未选择时显示"请选择"
3. **穿梭框选择**: 点击输入框打开穿梭框，支持搜索和批量选择
4. **状态管理**: 支持查看、编辑、新增等不同状态
5. **接口数据**: 从后端接口获取真实的污染源企业数据
6. **加载状态**: 显示数据加载状态，提升用户体验
7. **错误处理**: 接口失败时自动使用备用数据
8. **条件显示**: 只有开启溯源时才能选择，未开启时显示 "-"

## 使用方式

### 在 YTHForm.List 中使用

```tsx
{
  title: '溯源对象',
  name: 'traceObjects',
  minWidth: 120,
  edit: type !== 'view',
  display: dictKey === 'A22A08A06', // 只在环境空气质量监测中显示
  component: TraceObjectSelector,
  componentProps: (record, rowIndex) => {
    // 检查当前行是否开启了溯源
    const isTraceEnabled = record?.trace?.[0]?.code === '1' || record?.isTraceable === '1';

    return {
      value: record?.traceObjects || [],
      onChange: (value: string[]) => {
        // 更新表单数据
        const tempList = JSON.parse(JSON.stringify(form.values.list || []));
        if (tempList[rowIndex]) {
          tempList[rowIndex].traceObjects = value;
        }
        form.setValues({ list: tempList });
      },
      disabled: type === 'view',
      isTraceEnabled, // 传入溯源状态
      type,
    };
  },
}
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value | string[] | [] | 已选择的溯源对象ID数组 |
| onChange | (value: string[]) => void | - | 选择变化时的回调函数 |
| disabled | boolean | false | 是否禁用 |
| isTraceEnabled | boolean | false | 是否开启溯源功能 |
| type | 'view' \| 'edit' \| 'add' | 'edit' | 组件状态类型 |

## 数据结构

### 溯源对象数据结构
```tsx
interface TraceObject {
  key: string;        // 唯一标识
  title: string;      // 显示名称
  description: string; // 描述信息
}
```

### 接口数据
组件通过 `envRequest('/pollution-source/list')` 接口一次性获取所有污染源企业数据，支持最多1000条记录（推荐500条以内）。如果接口失败，会使用15个备用企业数据，包括：

- 云南安宁化工有限公司 - SO2排放量 (comp_001_hg)
- 安宁钢铁集团有限公司 - NOx排放量 (comp_002_gt)
- 安宁电力发展有限公司 - 烟尘排放量 (comp_003_dl)
- 云南安宁水泥股份有限公司 - 颗粒物排放量 (comp_004_sn)
- 等15家不同行业的企业及其对应的溯源指标

备用数据包含公司名称和溯源指标两个字段，使用真实的企业ID格式。

## 显示效果

- **未开启溯源**: 显示 "-"，输入框禁用，灰色背景
- **开启溯源 + 未选择**: 显示 "请选择"，可点击
- **开启溯源 + 已选择**: 显示数字，如 "3"（表示选择了3个对象）
- **查看模式**: 输入框变为灰色背景，不可点击
- **编辑模式**: 输入框可点击，鼠标悬停显示指针

## 穿梭框显示格式

穿梭框中每个选项显示两行信息：
- **第一行**: 公司名称（粗体，14px）
- **第二行**: 溯源指标（灰色，12px）

示例：
```
云南安宁化工有限公司
SO2排放量
```

## 数据处理

- **接口获取**: 组件挂载时自动调用 `/pollution-source/list` 接口获取数据
- **数据转换**: 自动将接口返回的数据转换为穿梭框需要的格式
- **备用数据**: 接口失败时使用15个备用企业数据，确保功能可用
- **格式转换**:
  - **读取时**: 后端返回的字符串格式自动解析为数组 `JSON.parse(traceObjects)`
  - **保存时**: 数组格式自动转换为字符串 `JSON.stringify(traceObjects)`
- **自动默认值**: `traceObjects` 字段在没有值时自动默认为空数组 `[]`
- **无需手动初始化**: 添加新行时不需要手动设置溯源对象字段
- **数量自动计算**: 显示的数量通过 `value.length` 自动计算，不需要单独存储

## 接口配置

### 接口地址
```
POST /pollution-source/list
```

### 请求参数
```json
{
  "currentPage": 1,
  "pageSize": 100,
  "condition": {
    // 可以添加查询条件，如企业类型、行业等
  }
}
```

### 分页加载流程
1. **首次加载**: `currentPage=1, pageSize=100`
2. **加载更多**: `currentPage=2, pageSize=100` (累加到现有数据)
3. **继续加载**: `currentPage=3, pageSize=100` (继续累加)
4. **加载完成**: 当返回数据 < 100条时，隐藏"加载更多"按钮

### 响应格式
```json
{
  "list": [
    {
      "id": "comp_001_hg",
      "name": "云南安宁化工有限公司",
      "companyName": "云南安宁化工有限公司",
      "unitName": "云南安宁化工有限公司",
      "industryType": "化工企业",
      "type": "化工企业",
      "description": "化工企业",
      "traceIndex": "SO2排放量",
      "indexName": "SO2排放量",
      "indicator": "SO2排放量"
    }
  ]
}
```

### 数据映射规则
- **key**: 优先使用 `item.id`，如果没有则生成 `trace_${page}_${index}` 格式
- **title**: 格式为 `${公司名称} - ${溯源指标}`
- **company**: 优先级 `item.name` > `item.companyName` > `item.unitName` > `企业${index + 1}`
- **traceIndex**: 优先级 `item.traceIndex` > `item.indexName` > `item.indicator` > `未知指标`
- **description**: 优先级 `item.industryType` > `item.type` > `item.description` > `企业`

## 数据格式说明

### 前端组件内部格式
```javascript
// 组件内部使用数组格式
traceObjects: ["comp_001_hg", "comp_002_gt", "comp_003_dl"]
```

### 后端存储格式
```javascript
// 后端存储使用字符串格式
traceObjects: '["comp_001_hg", "comp_002_gt", "comp_003_dl"]'
```

### 自动转换机制
- **读取数据时**: 如果后端返回字符串，自动使用 `JSON.parse()` 转换为数组
- **保存数据时**: 如果前端是数组，自动使用 `JSON.stringify()` 转换为字符串
- **错误处理**: 解析失败时自动使用空数组，确保组件正常工作

## 数据量与性能

### 分页累加加载方案
适合 **500-1000条** 数据（如50家公司 × 10+指标）

### 加载策略
- **首次加载**: 100条数据，快速显示
- **按需加载**: 点击"加载更多"继续加载
- **累加显示**: 保持已选择状态，用户体验好

### 性能优化配置
```typescript
// 分页累加配置
fetchTraceData(page, isLoadMore) {
  pageSize: 100,          // 每次加载100条
  累加模式: true,         // 保持已加载数据
}

pagination={{
  pageSize: 50,           // 每页显示50条
  showSizeChanger: true,  // 允许用户调整页面大小
}}
```

## 注意事项

1. **溯源状态判断**: 只有当 `isTraceEnabled` 为 `true` 时才能选择溯源对象
2. **未开启溯源**: 显示 "-"，输入框禁用，不可点击
3. **数据加载策略**: 一次性加载所有数据，适合500条以内的数据量
4. 在查看模式下，输入框会被禁用
5. 穿梭框支持搜索功能，方便快速查找目标对象
6. `traceObjects` 字段会自动默认为空数组，无需手动初始化
7. 不需要单独存储 `traceObjectsCount` 字段，数量通过数组长度自动计算
8. 接口失败时会自动使用备用数据，确保功能可用
9. 数据格式会自动在数组和字符串之间转换，无需手动处理
10. 可以根据实际接口调整数据字段映射关系
