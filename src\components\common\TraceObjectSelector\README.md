# 溯源对象选择器组件

## 功能描述

这是一个用于选择溯源对象的组件，主要用于环境质量监测中的溯源功能。当监测指标开启溯源时，可以通过此组件选择相关的污染源企业。

## 主要特性

1. **输入框样式**: 使用输入框样式显示，界面更统一
2. **数字显示**: 只显示选择的对象数量，未选择时显示"请选择"
3. **穿梭框选择**: 点击输入框打开穿梭框，支持搜索和批量选择
4. **状态管理**: 支持查看、编辑、新增等不同状态
5. **接口数据**: 从后端接口获取真实的污染源企业数据
6. **加载状态**: 显示数据加载状态，提升用户体验
7. **错误处理**: 接口失败时自动使用备用数据
8. **始终可用**: 不需要判断是否开启溯源，始终可以选择溯源对象

## 使用方式

### 在 YTHForm.List 中使用

```tsx
{
  title: '溯源对象',
  name: 'traceObjects',
  minWidth: 120,
  edit: type !== 'view',
  display: dictKey === 'A22A08A06', // 只在环境空气质量监测中显示
  component: TraceObjectSelector,
  componentProps: (record, rowIndex) => {
    return {
      value: record?.traceObjects || [],
      onChange: (value: string[]) => {
        // 更新表单数据
        const tempList = JSON.parse(JSON.stringify(form.values.list || []));
        if (tempList[rowIndex]) {
          tempList[rowIndex].traceObjects = value;
        }
        form.setValues({ list: tempList });
      },
      disabled: type === 'view',
      type,
    };
  },
}
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value | string[] | [] | 已选择的溯源对象ID数组 |
| onChange | (value: string[]) => void | - | 选择变化时的回调函数 |
| disabled | boolean | false | 是否禁用 |
| type | 'view' \| 'edit' \| 'add' | 'edit' | 组件状态类型 |

## 数据结构

### 溯源对象数据结构
```tsx
interface TraceObject {
  key: string;        // 唯一标识
  title: string;      // 显示名称
  description: string; // 描述信息
}
```

### 接口数据
组件通过 `envRequest('/pollution-source/list')` 接口获取真实的污染源企业数据，支持最多50条记录。如果接口失败，会使用15个备用企业数据，包括化工、钢铁、电力、水泥、造纸、石化、纺织、食品、医药、建材、机械、电子、轻工、有色金属、煤炭等不同类型的企业。

## 显示效果

- **未选择时**: 显示 "请选择"
- **已选择时**: 显示数字，如 "3"（表示选择了3个对象）
- **查看模式**: 输入框变为灰色背景，不可点击
- **编辑模式**: 输入框可点击，鼠标悬停显示指针

## 数据处理

- **接口获取**: 组件挂载时自动调用 `/pollution-source/list` 接口获取数据
- **数据转换**: 自动将接口返回的数据转换为穿梭框需要的格式
- **备用数据**: 接口失败时使用15个备用企业数据，确保功能可用
- **自动默认值**: `traceObjects` 字段在没有值时自动默认为空数组 `[]`
- **无需手动初始化**: 添加新行时不需要手动设置溯源对象字段
- **数量自动计算**: 显示的数量通过 `value.length` 自动计算，不需要单独存储

## 接口配置

### 接口地址
```
POST /pollution-source/list
```

### 请求参数
```json
{
  "currentPage": 1,
  "pageSize": 50,
  "condition": {
    // 可以添加查询条件
  }
}
```

### 响应格式
```json
{
  "list": [
    {
      "id": "企业ID",
      "name": "企业名称",
      "companyName": "公司名称",
      "unitName": "单位名称",
      "industryType": "行业类型",
      "type": "企业类型",
      "description": "描述"
    }
  ]
}
```

## 注意事项

1. 组件始终显示选择功能，不需要判断是否开启溯源
2. 在查看模式下，输入框会被禁用
3. 穿梭框支持搜索功能，方便快速查找目标对象
4. `traceObjects` 字段会自动默认为空数组，无需手动初始化
5. 不需要单独存储 `traceObjectsCount` 字段，数量通过数组长度自动计算
6. 接口失败时会自动使用备用数据，确保功能可用
7. 可以根据实际接口调整数据字段映射关系
