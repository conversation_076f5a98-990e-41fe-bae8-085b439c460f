# 溯源对象选择器组件

## 功能描述

这是一个用于选择溯源对象的组件，主要用于环境质量监测中的溯源功能。当监测指标开启溯源时，可以通过此组件选择相关的污染源企业。

## 主要特性

1. **穿梭框选择**: 点击按钮打开穿梭框，支持搜索和批量选择
2. **数量显示**: 显示已选择的对象数量
3. **状态管理**: 支持查看、编辑、新增等不同状态
4. **模拟数据**: 使用模拟的污染源企业数据
5. **始终可用**: 不需要判断是否开启溯源，始终可以选择溯源对象

## 使用方式

### 在 YTHForm.List 中使用

```tsx
{
  title: '溯源对象',
  name: 'traceObjects',
  minWidth: 120,
  edit: type !== 'view',
  display: dictKey === 'A22A08A06', // 只在环境空气质量监测中显示
  component: TraceObjectSelector,
  componentProps: (record, rowIndex) => {
    return {
      value: record?.traceObjects || [],
      onChange: (value: string[]) => {
        // 更新表单数据
        const tempList = JSON.parse(JSON.stringify(form.values.list || []));
        if (tempList[rowIndex]) {
          tempList[rowIndex].traceObjects = value;
          tempList[rowIndex].traceObjectsCount = value.length;
        }
        form.setValues({ list: tempList });
      },
      disabled: type === 'view',
      type,
    };
  },
}
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value | string[] | [] | 已选择的溯源对象ID数组 |
| onChange | (value: string[]) => void | - | 选择变化时的回调函数 |
| disabled | boolean | false | 是否禁用 |
| isTraceEnabled | boolean | false | 是否开启溯源功能 |
| type | 'view' \| 'edit' \| 'add' | 'edit' | 组件状态类型 |

## 数据结构

### 溯源对象数据结构
```tsx
interface TraceObject {
  key: string;        // 唯一标识
  title: string;      // 显示名称
  description: string; // 描述信息
}
```

### 模拟数据
组件内置了8个模拟的污染源企业数据，包括化工、钢铁、电力、水泥、造纸、石化、纺织、食品等不同类型的企业。

## 注意事项

1. 组件只在 `isTraceEnabled` 为 `true` 时显示选择功能
2. 在查看模式下，按钮会被禁用
3. 穿梭框支持搜索功能，方便快速查找目标对象
4. 实际项目中应该将模拟数据替换为真实的API接口数据
