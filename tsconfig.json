{"compileOnSave": false, "buildOnSave": false, "compilerOptions": {"baseUrl": "./", "moduleResolution": "node", "target": "esnext", "module": "esnext", "jsx": "react", "esModuleInterop": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "skipLibCheck": true, "declaration": true, "outDir": "build", "paths": {"@/*": ["src/*"]}, "types": []}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx"], "exclude": ["node_modules", "build", "public"]}